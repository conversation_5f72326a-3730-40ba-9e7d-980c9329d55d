// Unit tests for MCPServer
import { MCPServer } from '../../../../src/mcp-server/core/mcp-server';
import { mockMCPConfig } from '../../mocks/config.mock';
import express from 'express';
import http from 'http';
import { Server as SocketIOServer } from 'socket.io';

// Mock express
jest.mock('express', () => {
  const mockExpress = jest.fn(() => ({
    use: jest.fn(),
    get: jest.fn(),
    listen: jest.fn()
  }));
  mockExpress.json = jest.fn().mockReturnValue('json-middleware');
  mockExpress.urlencoded = jest.fn().mockReturnValue('urlencoded-middleware');
  mockExpress.static = jest.fn().mockReturnValue('static-middleware');
  return mockExpress;
});

// Mock http
jest.mock('http', () => ({
  createServer: jest.fn().mockReturnValue({
    listen: jest.fn(),
    close: jest.fn()
  })
}));

// Mock socket.io
jest.mock('socket.io', () => ({
  Server: jest.fn().mockImplementation(() => ({
    on: jest.fn(),
    to: jest.fn().mockReturnThis(),
    emit: jest.fn()
  }))
}));

// Mock middleware
jest.mock('../../../../src/mcp-server/middleware/mcp-auth', () => ({
  MCPAuthMiddleware: jest.fn().mockReturnValue('auth-middleware')
}));

jest.mock('../../../../src/mcp-server/middleware/mcp-error-handler', () => ({
  MCPErrorHandler: jest.fn().mockReturnValue('error-handler-middleware')
}));

// Mock routes
jest.mock('../../../../src/mcp-server/routes/mcp-router', () => ({
  MCPRouter: jest.fn().mockReturnValue('mcp-router')
}));

// Mock utils
jest.mock('../../../../src/mcp-server/utils/mcp-logger', () => ({
  MCPLogger: jest.fn().mockImplementation(() => ({
    info: jest.fn(),
    error: jest.fn()
  }))
}));

jest.mock('../../../../src/mcp-server/utils/mcp-metrics', () => ({
  MCPMetricsCollector: jest.fn().mockImplementation(() => ({
    recordRequest: jest.fn(),
    getMetrics: jest.fn()
  }))
}));

// Mock services
jest.mock('../../../../src/mcp-server/services/mcp-cache', () => ({
  MCPCache: jest.fn().mockImplementation(() => ({
    close: jest.fn()
  }))
}));

// Mock helmet
jest.mock('helmet', () => jest.fn().mockReturnValue('helmet-middleware'));

// Mock cors
jest.mock('cors', () => jest.fn().mockReturnValue('cors-middleware'));

// Mock rate-limit
jest.mock('express-rate-limit', () => jest.fn().mockReturnValue('rate-limit-middleware'));

describe('MCPServer', () => {
  let server: MCPServer;
  let mockApp: any;
  let mockHttpServer: any;
  let mockIo: any;
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    server = new MCPServer(mockMCPConfig);
    
    // Get mocked instances
    mockApp = (express as jest.Mock)();
    mockHttpServer = (http.createServer as jest.Mock)();
    mockIo = (SocketIOServer as jest.Mock).mock.instances[0];
  });
  
  describe('constructor', () => {
    test('should initialize server components', () => {
      expect(express).toHaveBeenCalled();
      expect(http.createServer).toHaveBeenCalledWith(mockApp);
      expect(SocketIOServer).toHaveBeenCalledWith(mockHttpServer, expect.any(Object));
      
      // Verify middleware setup
      expect(mockApp.use).toHaveBeenCalledWith('helmet-middleware');
      expect(mockApp.use).toHaveBeenCalledWith('cors-middleware');
      expect(mockApp.use).toHaveBeenCalledWith('rate-limit-middleware');
      expect(mockApp.use).toHaveBeenCalledWith('json-middleware');
      expect(mockApp.use).toHaveBeenCalledWith('urlencoded-middleware');
      expect(mockApp.use).toHaveBeenCalledWith('auth-middleware');
      
      // Verify routes setup
      expect(mockApp.get).toHaveBeenCalledWith('/health', expect.any(Function));
      expect(mockApp.get).toHaveBeenCalledWith('/metrics', expect.any(Function));
      expect(mockApp.use).toHaveBeenCalledWith('/mcp', 'mcp-router');
      
      // Verify socket.io setup
      expect(mockIo.on).toHaveBeenCalledWith('connection', expect.any(Function));
      
      // Verify error handling setup
      expect(mockApp.use).toHaveBeenCalledWith(expect.any(Function)); // 404 handler
      expect(mockApp.use).toHaveBeenCalledWith('error-handler-middleware');
    });
  });
  
  describe('start', () => {
    test('should start the server', () => {
      server.start();
      
      expect(mockHttpServer.listen).toHaveBeenCalledWith(
        mockMCPConfig.port,
        expect.any(Function)
      );
    });
  });
  
  describe('stop', () => {
    test('should stop the server', async () => {
      // Mock server.close to call callback
      mockHttpServer.close.mockImplementation((callback) => {
        callback();
      });
      
      await server.stop();
      
      expect(mockHttpServer.close).toHaveBeenCalled();
    });
    
    test('should handle server close errors', async () => {
      // Mock server.close to call callback with error
      mockHttpServer.close.mockImplementation((callback) => {
        callback(new Error('Server close error'));
      });
      
      await expect(server.stop()).rejects.toThrow('Server close error');
    });
  });
  
  describe('broadcastProgress', () => {
    test('should broadcast progress to clients', () => {
      const jobId = 'test-job-id';
      const progress = { completed: 50, total: 100 };
      
      server.broadcastProgress(jobId, progress);
      
      expect(mockIo.to).toHaveBeenCalledWith(`job-${jobId}`);
      expect(mockIo.emit).toHaveBeenCalledWith('progress', progress);
    });
  });
});