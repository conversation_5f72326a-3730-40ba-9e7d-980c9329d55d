{"config": {"configFile": "C:\\dev\\legacy-bridge\\legacybridge\\playwright.config.mcp.ts", "rootDir": "C:/dev/legacy-bridge/legacybridge/tests/mcp-server/playwright", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 6}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report-mcp"}], ["json", {"outputFile": "test-results-mcp.json"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/dev/legacy-bridge/legacybridge/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "api-tests", "name": "api-tests", "testDir": "C:/dev/legacy-bridge/legacybridge/tests/mcp-server/playwright", "testIgnore": [], "testMatch": ["**/*.spec.ts"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 6, "webServer": null}, "suites": [{"title": "mcp-components.spec.ts", "file": "mcp-components.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "MCP Server Components", "file": "mcp-components.spec.ts", "line": 10, "column": 6, "specs": [{"title": "MCPConfig should load with default values", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 20, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-29T23:37:53.626Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4123bba5c2a11198ff62-2ee55f2d04658a447667", "file": "mcp-components.spec.ts", "line": 12, "column": 7}, {"title": "MCPLogger should create log entries", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 33, "errors": [], "stdout": [{"text": "2025-07-29T23:37:53.676Z [\u001b[31merror\u001b[39m]: Test error message {\"service\":\"mcp-server\"}\r\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-29T23:37:53.639Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4123bba5c2a11198ff62-8e81179c20651697c71c", "file": "mcp-components.spec.ts", "line": 24, "column": 7}, {"title": "MCPCache should handle memory caching", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 16, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-29T23:37:53.641Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4123bba5c2a11198ff62-f4f09f83377baa60cefb", "file": "mcp-components.spec.ts", "line": 36, "column": 7}, {"title": "MCPCache should handle disabled state", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 11, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-29T23:37:53.678Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4123bba5c2a11198ff62-fdd41fb10daf50dfca16", "file": "mcp-components.spec.ts", "line": 57, "column": 7}, {"title": "MCPMetricsCollector should track metrics", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 18, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-29T23:37:53.625Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4123bba5c2a11198ff62-cb64752a25755d0cf5a2", "file": "mcp-components.spec.ts", "line": 73, "column": 7}, {"title": "MCPMetricsCollector should calculate success rates", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 11, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-29T23:37:53.732Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4123bba5c2a11198ff62-53d92b3e755554067dc9", "file": "mcp-components.spec.ts", "line": 102, "column": 7}, {"title": "MCPConfig should validate configuration", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 9, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-29T23:37:53.658Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4123bba5c2a11198ff62-befcd09b221ae51fc49e", "file": "mcp-components.spec.ts", "line": 128, "column": 7}, {"title": "MCPCache should handle cache key generation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 6, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-29T23:37:53.659Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4123bba5c2a11198ff62-5913ca32ce5af8d5bf10", "file": "mcp-components.spec.ts", "line": 138, "column": 7}, {"title": "MCPLogger should handle different log levels", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 29, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-29T23:37:53.679Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4123bba5c2a11198ff62-d88115ed69cdf324e928", "file": "mcp-components.spec.ts", "line": 162, "column": 7}, {"title": "MCPMetricsCollector should handle batch metrics", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 7, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-29T23:37:53.680Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4123bba5c2a11198ff62-b6fcbbe69abb205f30fd", "file": "mcp-components.spec.ts", "line": 176, "column": 7}, {"title": "MCPConfig should handle environment variables", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "api-tests", "projectName": "api-tests", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 4, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-29T23:37:53.680Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4123bba5c2a11198ff62-e784c969296d14132f87", "file": "mcp-components.spec.ts", "line": 197, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-07-29T23:37:52.223Z", "duration": 1549.7960000000003, "expected": 11, "skipped": 0, "unexpected": 0, "flaky": 0}}