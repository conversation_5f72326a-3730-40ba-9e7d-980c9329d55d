// MCP Server Metrics Collector
// Collects and exposes metrics for the MCP server

export interface RequestMetric {
  method: string;
  path: string;
  statusCode: number;
  duration: number;
}

export interface ConversionMetric {
  format: string;
  success: boolean;
  duration: number;
  fileSize: number;
}

export interface BatchMetric {
  fileCount: number;
  successCount: number;
  failureCount: number;
  totalSize: number;
  duration: number;
}

export interface CacheMetric {
  hits: number;
  misses: number;
  size: number;
  hitRate: number;
}

export class MCPMetricsCollector {
  private startTime: number;
  private requestCount: number = 0;
  private requestDurations: number[] = [];
  private statusCodes: Record<number, number> = {};
  private endpoints: Record<string, number> = {};
  private conversions: ConversionMetric[] = [];
  private batches: BatchMetric[] = [];
  private cacheMetrics: CacheMetric = {
    hits: 0,
    misses: 0,
    size: 0,
    hitRate: 0,
  };
  
  // Metrics for different file formats
  private formatMetrics: Record<string, {
    count: number;
    successCount: number;
    failureCount: number;
    totalDuration: number;
    totalSize: number;
  }> = {};

  constructor() {
    this.startTime = Date.now();
  }

  // Record a request
  public recordRequest(metric: RequestMetric): void {
    this.requestCount++;
    this.requestDurations.push(metric.duration);
    
    // Update status code counts
    this.statusCodes[metric.statusCode] = (this.statusCodes[metric.statusCode] || 0) + 1;
    
    // Update endpoint counts
    const endpoint = `${metric.method} ${metric.path}`;
    this.endpoints[endpoint] = (this.endpoints[endpoint] || 0) + 1;
  }

  // Record a conversion
  public recordConversion(metric: ConversionMetric): void {
    this.conversions.push(metric);
    
    // Update format metrics
    if (!this.formatMetrics[metric.format]) {
      this.formatMetrics[metric.format] = {
        count: 0,
        successCount: 0,
        failureCount: 0,
        totalDuration: 0,
        totalSize: 0,
      };
    }
    
    const formatMetric = this.formatMetrics[metric.format];
    formatMetric.count++;
    if (metric.success) {
      formatMetric.successCount++;
    } else {
      formatMetric.failureCount++;
    }
    formatMetric.totalDuration += metric.duration;
    formatMetric.totalSize += metric.fileSize;
  }

  // Record a batch job
  public recordBatch(metric: BatchMetric): void {
    this.batches.push(metric);
  }

  // Record cache metrics
  public recordCacheHit(): void {
    this.cacheMetrics.hits++;
    this.updateCacheHitRate();
  }

  public recordCacheMiss(): void {
    this.cacheMetrics.misses++;
    this.updateCacheHitRate();
  }

  public setCacheSize(size: number): void {
    this.cacheMetrics.size = size;
  }

  private updateCacheHitRate(): void {
    const total = this.cacheMetrics.hits + this.cacheMetrics.misses;
    this.cacheMetrics.hitRate = total > 0 ? this.cacheMetrics.hits / total : 0;
  }

  // Get all metrics
  public getMetrics(): any {
    const uptime = Math.floor((Date.now() - this.startTime) / 1000); // in seconds
    
    // Calculate request statistics
    const avgRequestDuration = this.requestDurations.length > 0
      ? this.requestDurations.reduce((sum, duration) => sum + duration, 0) / this.requestDurations.length
      : 0;
    
    // Calculate conversion statistics
    const successfulConversions = this.conversions.filter(c => c.success);
    const failedConversions = this.conversions.filter(c => !c.success);
    const conversionSuccessRate = this.conversions.length > 0
      ? successfulConversions.length / this.conversions.length
      : 0;
    
    // Calculate format statistics
    const formatStats = Object.entries(this.formatMetrics).map(([format, metrics]) => ({
      format,
      count: metrics.count,
      successRate: metrics.count > 0 ? metrics.successCount / metrics.count : 0,
      avgDuration: metrics.count > 0 ? metrics.totalDuration / metrics.count : 0,
      avgSize: metrics.count > 0 ? metrics.totalSize / metrics.count : 0,
    }));
    
    // Calculate batch statistics
    const avgBatchSize = this.batches.length > 0
      ? this.batches.reduce((sum, batch) => sum + batch.fileCount, 0) / this.batches.length
      : 0;
    const avgBatchSuccessRate = this.batches.length > 0
      ? this.batches.reduce((sum, batch) => sum + (batch.successCount / batch.fileCount), 0) / this.batches.length
      : 0;
    
    return {
      server: {
        uptime,
        startTime: new Date(this.startTime).toISOString(),
      },
      requests: {
        total: this.requestCount,
        avgDuration: avgRequestDuration,
        statusCodes: this.statusCodes,
        topEndpoints: Object.entries(this.endpoints)
          .sort((a, b) => b[1] - a[1])
          .slice(0, 10)
          .map(([endpoint, count]) => ({ endpoint, count })),
      },
      conversions: {
        total: this.conversions.length,
        successful: successfulConversions.length,
        failed: failedConversions.length,
        successRate: conversionSuccessRate,
        byFormat: formatStats,
      },
      batches: {
        total: this.batches.length,
        avgSize: avgBatchSize,
        avgSuccessRate: avgBatchSuccessRate,
      },
      cache: this.cacheMetrics,
    };
  }

  // Reset metrics (for testing)
  public reset(): void {
    this.startTime = Date.now();
    this.requestCount = 0;
    this.requestDurations = [];
    this.statusCodes = {};
    this.endpoints = {};
    this.conversions = [];
    this.batches = [];
    this.formatMetrics = {};
    this.cacheMetrics = {
      hits: 0,
      misses: 0,
      size: 0,
      hitRate: 0,
    };
  }
}